"use client"

import React, { useState, useEffect, useRef, useMemo, useCallback, useContext } from "react"
import { AuthContext } from "../context/AuthContext"
import {
  Dialog,
  DialogType,
  IconButton,
  Text,
  mergeStyleSets,
  FontWeights,
  getTheme,
  Persona,
  PersonaSize,
  Spinner,
  SpinnerSize,
  TooltipHost,
} from "@fluentui/react"

// Styles for the video call component
const theme = getTheme()
const styles = mergeStyleSets({
  callDialog: {
    width: "85%", // Increased from 70%
    maxWidth: "1400px", // Increased from 1200px
    height: "80vh", // Increased from 70vh
    maxHeight: "900px", // Increased from 800px
    borderRadius: "12px",
    overflow: "hidden",
    backgroundColor: "#202124", // Google Meet dark background
    border: "none",
    padding: 0,
    margin: 0,
  },
  callHeader: {
    backgroundColor: "rgba(32, 33, 36, 0.9)",
    backdropFilter: "blur(10px)",
    padding: "14px 24px", // Increased from 12px 20px
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    color: "white",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    boxShadow: "0 2px 6px rgba(0, 0, 0, 0.2)",
    "@media (max-width: 768px)": {
      padding: "12px 20px",
    },
    "@media (max-width: 576px)": {
      padding: "10px 16px",
    },
  },
  headerLeft: {
    display: "flex",
    alignItems: "center",
  },
  headerRight: {
    display: "flex",
    alignItems: "center",
  },
  personaContainer: {
    marginRight: "12px",
    display: "flex",
    alignItems: "center",
  },
  callStatusText: {
    fontSize: "14px",
    fontWeight: FontWeights.semibold,
    color: theme.palette.white,
  },
  callDurationText: {
    fontSize: "14px",
    marginLeft: "12px",
    color: theme.palette.white,
  },
  callBody: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    width: "100%",
    backgroundColor: "#202124",
    position: "relative",
  },
  videoContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
    width: "100%",
    position: "relative",
    padding: "60px 0 80px", // Make room for header and controls
    "@media (max-width: 768px)": {
      padding: "50px 0 70px", // Slightly reduced padding for mobile
    },
  },
  localVideoContainer: {
    position: "absolute",
    width: "280px", // Increased from 250px
    height: "210px", // Increased from 180px
    bottom: "100px",
    right: "20px",
    borderRadius: "12px",
    overflow: "hidden",
    border: "2px solid rgba(255, 255, 255, 0.2)",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.4)",
    zIndex: 2,
    transition: "all 0.3s ease",
    "@media (max-width: 1200px)": {
      width: "220px",
      height: "165px",
      bottom: "100px",
      right: "20px",
    },
    "@media (max-width: 992px)": {
      width: "180px",
      height: "135px",
      bottom: "90px",
      right: "15px",
    },
    "@media (max-width: 768px)": {
      width: "140px",
      height: "105px",
      bottom: "90px",
      right: "10px",
    },
    "@media (max-width: 576px)": {
      width: "120px",
      height: "90px",
      bottom: "80px",
      right: "10px",
    },
  },
  remoteVideoContainer: {
    width: "100%",
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    overflow: "hidden",
    backgroundColor: "#202124",
    borderRadius: "12px",
    zIndex: 1,
    minHeight: "500px", // Increased from 400px
    "@media (max-width: 992px)": {
      minHeight: "450px",
    },
    "@media (max-width: 768px)": {
      minHeight: "400px",
    },
    "@media (max-width: 576px)": {
      minHeight: "350px",
    },
  },
  videoPlaceholder: {
    width: "100%",
    height: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#3c4043",
    color: "white",
    fontSize: "20px",
    borderRadius: "12px",
  },
  avatarPlaceholder: {
    width: "120px",
    height: "120px",
    borderRadius: "50%",
    backgroundColor: theme.palette.themePrimary,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    color: "white",
    fontSize: "48px",
    fontWeight: FontWeights.semibold,
    marginBottom: "16px",
  },
  callControls: {
    display: "flex",
    justifyContent: "center",
    gap: "20px", // Increased from 16px
    padding: "18px", // Increased from 16px
    backgroundColor: "rgba(32, 33, 36, 0.9)",
    backdropFilter: "blur(10px)",
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    boxShadow: "0 -2px 6px rgba(0, 0, 0, 0.2)",
    "@media (max-width: 992px)": {
      gap: "16px",
      padding: "16px",
    },
    "@media (max-width: 768px)": {
      gap: "12px",
      padding: "14px 10px",
    },
    "@media (max-width: 576px)": {
      gap: "8px",
      padding: "12px 8px",
    },
  },
  controlButton: {
    width: "54px", // Increased from 48px
    height: "54px", // Increased from 48px
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    border: "none",
    transition: "all 0.2s ease",
    "@media (max-width: 992px)": {
      width: "48px",
      height: "48px",
    },
    "@media (max-width: 768px)": {
      width: "44px",
      height: "44px",
    },
    "@media (max-width: 576px)": {
      width: "40px",
      height: "40px",
    },
  },
  endCallButton: {
    backgroundColor: "#ea4335", // Google Meet red
    color: "white",
    "&:hover": {
      backgroundColor: "#d93025",
    },
  },
  toggleButton: {
    backgroundColor: "#3c4043", // Dark gray
    color: "white",
    "&:hover": {
      backgroundColor: "#5f6368",
    },
  },
  disabledButton: {
    backgroundColor: "#ea4335", // Red for disabled
    color: "white",
    "&:hover": {
      backgroundColor: "#d93025",
    },
  },
  iconStyles: {
    fontSize: "22px", // Increased from 20px
    "@media (max-width: 992px)": {
      fontSize: "20px",
    },
    "@media (max-width: 768px)": {
      fontSize: "18px",
    },
    "@media (max-width: 576px)": {
      fontSize: "16px",
    },
  },
  connectingContainer: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
    width: "100%",
    backgroundColor: "#202124",
    color: "white",
  },
  connectingSpinner: {
    marginBottom: "20px",
  },
  connectingText: {
    fontSize: "20px",
    fontWeight: FontWeights.semibold,
    marginBottom: "16px",
  },
  connectingSubText: {
    fontSize: "16px",
    color: "rgba(255, 255, 255, 0.7)",
    maxWidth: "400px",
    textAlign: "center",
  },
  networkQualityIndicator: {
    display: "flex",
    alignItems: "center",
    marginLeft: "12px",
    gap: "2px",
  },
  networkBar: {
    width: "4px",
    height: "8px",
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: "2px",
    transition: "all 0.3s ease",
  },
  networkBarActive: {
    backgroundColor: "#8ab4f8", // Light blue for active
  },
  remoteUserInfo: {
    position: "absolute",
    bottom: "20px",
    left: "20px",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: "8px 12px",
    borderRadius: "20px",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    zIndex: 3,
  },
  remoteUserName: {
    color: "white",
    fontWeight: FontWeights.semibold,
    fontSize: "14px",
  },
  remoteAudioStatus: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
    color: "white",
    fontSize: "12px",
  },
});

// Enum for call status descriptions
const CallStatusText = {
  idle: "Call Ended",
  connecting: "Connecting...",
  connected: "Connected",
  disconnected: "Call Disconnected",
};

/**
 * VideoCallComponent displays the active video call UI
 */
const VideoCallComponent = ({
  activeCall,
  callStatus,
  callDuration,
  localVideoTrack,
  localAudioTrack,
  remoteUsers,
  isVideoEnabled,
  isAudioEnabled,
  onEndCall,
  onToggleVideo,
  onToggleAudio,
  formatCallDuration,
  networkQuality,
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [longConnecting, setLongConnecting] = useState(false);
  const [remoteVideoPlaying, setRemoteVideoPlaying] = useState({});
  const [tokenRefreshNeeded, setTokenRefreshNeeded] = useState(false);

  // Get auth context for token refresh
  const { refreshTokens, refreshRTCToken, tokens } = useContext(AuthContext);

  const localVideoRef = useRef(null);
  const remoteVideoRefs = useRef({});
  const tokenRefreshTimeoutRef = useRef(null);

  // Open dialog when there's an active call
  useEffect(() => {
    if (activeCall) {
      console.log("Opening video call dialog for:", activeCall.peerName);
      setIsDialogOpen(true);
      setLongConnecting(false);
    } else {
      setIsDialogOpen(false);
      setLongConnecting(false);
    }
  }, [activeCall]);

  // Set "long connecting" state if connecting takes more than 10 seconds
  useEffect(() => {
    let timer;
    if (callStatus === "connecting" && activeCall) {
      timer = setTimeout(() => {
        setLongConnecting(true);
      }, 10000);
    } else {
      setLongConnecting(false);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [callStatus, activeCall]);

  // Memoized function for playing local video to maintain stable reference
  const playLocalVideo = useCallback((videoTrack, container) => {
    if (!videoTrack || !container) return;

    try {
      // Only stop if we're sure it's playing in a different container
      try {
        if (videoTrack.isPlaying && container !== videoTrack._playDom) {
          videoTrack.stop();
        }
      } catch (stopErr) {
        console.warn("Non-critical: Error checking local video track state:", stopErr);
      }

      // Play the track with optimized settings
      videoTrack.play(container, {
        fit: "cover",
        mirror: true, // Mirror local video for natural selfie experience
        // Add optimization options
        optimizationMode: "detail",
        encoderConfig: {
          width: 640,
          height: 480,
          frameRate: 30,
          bitrateMin: 400,
          bitrateMax: 1000,
        }
      });
      console.log("Successfully playing local video track");
    } catch (err) {
      console.error("Error playing local video track:", err);
    }
  }, []);

  // Track if local video is already set up to avoid unnecessary re-renders
  const localVideoSetupRef = useRef(false);

  // Play local video track when available - with optimizations to prevent flickering
  useEffect(() => {
    // Skip if we don't have both the track and the container
    if (!localVideoTrack || !localVideoRef.current) return;

    // Skip if already set up with the same track
    if (localVideoSetupRef.current && localVideoTrack._playDom === localVideoRef.current) {
      return;
    }

    // Play the video
    playLocalVideo(localVideoTrack, localVideoRef.current);
    localVideoSetupRef.current = true;

    // Cleanup function - only runs when component unmounts or track changes
    return () => {
      try {
        // Only stop if we're unmounting or changing tracks
        if (localVideoTrack && localVideoTrack.isPlaying) {
          localVideoTrack.stop();
          console.log("Stopped local video track during cleanup");
        }
        localVideoSetupRef.current = false;
      } catch (err) {
        console.warn("Non-critical: Error stopping local video track during cleanup:", err);
      }
    };
  }, [localVideoTrack, playLocalVideo]);

  // Track video playback status with refs instead of state to prevent re-renders
  const playingTracksRef = useRef(new Map());
  const videoPlaybackTimeoutsRef = useRef({});

  // Memoize the remote users to prevent unnecessary re-renders
  const memoizedRemoteUsers = useMemo(() => remoteUsers, [
    // Only update when the users array length changes or when a user's video/audio status changes
    remoteUsers.length,
    // Create a stable dependency string that only changes when video/audio status changes
    remoteUsers.map(user => `${user.uid}-${user.hasVideo}-${user.hasAudio}`).join('|')
  ]);

  // Safe wrapper for playing video tracks - memoized to maintain reference stability
  const safePlayVideo = useCallback((videoTrack, container, uid) => {
    if (!videoTrack || !container) return false;

    try {
      // Only stop if we're sure it's playing to avoid flickering
      try {
        if (videoTrack.isPlaying && container !== videoTrack._playDom) {
          videoTrack.stop();
        }
      } catch (stopErr) {
        console.warn(`Non-critical: Error checking video state for user ${uid}:`, stopErr);
      }

      // Play the track with error boundary and optimized settings
      videoTrack.play(container, {
        fit: "cover",
        mirror: false,
        // Add optimization options
        optimizationMode: "detail",
        encoderConfig: {
          width: 640,
          height: 480,
          frameRate: 30,
          bitrateMin: 400,
          bitrateMax: 1000,
        }
      });

      // Mark as successfully playing in our ref
      playingTracksRef.current.set(uid, true);

      // Update state only once per user to avoid frequent re-renders
      if (!remoteVideoPlaying[uid]) {
        setRemoteVideoPlaying(prev => ({...prev, [uid]: true}));
      }

      return true;
    } catch (err) {
      console.error(`Error playing video for user ${uid}:`, err);
      playingTracksRef.current.set(uid, false);
      return false;
    }
  }, [remoteVideoPlaying]);

  // Play remote video tracks when available - with optimizations to prevent flickering
  useEffect(() => {
    // Only proceed if we have remote users and we're connected
    if (memoizedRemoteUsers.length === 0 || callStatus !== "connected") return;

    console.log("Setting up remote video handling for", memoizedRemoteUsers.length, "users");

    // Function to play a single user's video - avoids re-creating this function for each user
    const playUserVideo = (user) => {
      // Skip if no video track or if we've already marked it as successfully playing
      if (!user.videoTrack || playingTracksRef.current.get(user.uid) === true) {
        return;
      }

      // Get reference to the DOM element
      let container = remoteVideoRefs.current[user.uid];

      // If we don't have a ref, try to find by ID
      if (!container) {
        try {
          container = document.getElementById(`remote-video-${user.uid}`);
          if (container) {
            remoteVideoRefs.current[user.uid] = container;
          }
        } catch (err) {
          console.warn(`Error finding container for user ${user.uid}:`, err);
        }
      }

      if (!container) {
        return; // Skip if we still don't have a container
      }

      // Ensure container has proper dimensions
      if (container.clientHeight === 0) {
        container.style.height = "400px";
        container.style.minHeight = "400px";
      }

      // Play the video
      safePlayVideo(user.videoTrack, container, user.uid);
    };

    // Initial setup for all users
    memoizedRemoteUsers.forEach(user => {
      if (user.hasVideo && user.videoTrack) {
        // Clear any existing timeout for this user
        if (videoPlaybackTimeoutsRef.current[user.uid]) {
          clearTimeout(videoPlaybackTimeoutsRef.current[user.uid]);
        }

        // Set a small delay to ensure DOM is ready, but use a different timeout for each user
        videoPlaybackTimeoutsRef.current[user.uid] = setTimeout(() => {
          playUserVideo(user);
        }, 200);
      }
    });

    // Set up a single retry mechanism that's less aggressive
    let retryCount = 0;
    const maxRetries = 3;

    const retryInterval = setInterval(() => {
      // Check if we need to retry any tracks
      const needsRetry = memoizedRemoteUsers.some(user =>
        user.videoTrack &&
        user.hasVideo &&
        playingTracksRef.current.get(user.uid) !== true
      );

      if (needsRetry && retryCount < maxRetries) {
        console.log(`Retrying unplayed videos (attempt ${retryCount + 1}/${maxRetries})`);

        // Only retry users that need it
        memoizedRemoteUsers.forEach(user => {
          if (user.videoTrack &&
              user.hasVideo &&
              playingTracksRef.current.get(user.uid) !== true) {
            playUserVideo(user);
          }
        });

        retryCount++;
      } else {
        clearInterval(retryInterval);
      }
    }, 2000); // Less frequent retries

    // Clean up function
    return () => {
      // Clear all timeouts
      Object.values(videoPlaybackTimeoutsRef.current).forEach(timeout => {
        clearTimeout(timeout);
      });
      videoPlaybackTimeoutsRef.current = {};

      // Clear interval
      clearInterval(retryInterval);

      // We don't stop video tracks here anymore to prevent flickering
      // when this effect re-runs due to dependency changes
      // The tracks will be properly cleaned up when the component unmounts
    };
  }, [memoizedRemoteUsers, callStatus, safePlayVideo]);

  // Token refresh effect for active calls
  useEffect(() => {
    // Only set up token refresh for active calls
    if (callStatus !== "connected" || !activeCall) {
      // Clear any existing timeout
      if (tokenRefreshTimeoutRef.current) {
        clearTimeout(tokenRefreshTimeoutRef.current);
        tokenRefreshTimeoutRef.current = null;
      }
      return;
    }

    console.log("Setting up token refresh for active video call");

    // Function to check and refresh token if needed
    const checkAndRefreshToken = async () => {
      try {
        // Check if RTC token is about to expire
        const rtcTokenAge = tokens?.rtcTokenCreatedAt ? Date.now() - tokens.rtcTokenCreatedAt : null;
        const maxTokenAge = 50 * 60 * 1000; // 50 minutes

        if (!tokens?.rtcToken || !tokens?.rtcTokenCreatedAt || rtcTokenAge > maxTokenAge) {
          console.log("RTC token needs refresh during active video call");

          // For active calls, use channel-specific token refresh
          const channelName = activeCall?.channelName;
          if (channelName) {
            console.log(`Refreshing channel-specific RTC token for active call: ${channelName}`);

            try {
              // Fetch fresh channel-specific token
              const tokenResult = await refreshRTCToken(channelName);

              if (tokenResult && tokenResult.rtcToken) {
                console.log("Successfully refreshed channel-specific RTC token during active video call");
                setTokenRefreshNeeded(false);

                // Update the video call service with the new token
                // This requires access to the video call service instance
                // The useVideoCall hook should handle this through a token update mechanism
                return;
              }
            } catch (channelTokenError) {
              console.warn("Failed to refresh channel-specific token, falling back to generic refresh:", channelTokenError);
            }
          }

          // Fallback: Try to refresh generic RTC token first (faster)
          let success = await refreshRTCToken();

          // If that fails, try refreshing all tokens
          if (!success) {
            success = await refreshTokens();
          }

          if (success) {
            console.log("Successfully refreshed tokens during active video call");
            setTokenRefreshNeeded(false);
          } else {
            console.warn("Failed to refresh tokens during active video call");
            setTokenRefreshNeeded(true);
          }
        }
      } catch (error) {
        console.error("Error refreshing tokens during active video call:", error);
        setTokenRefreshNeeded(true);
      }

      // Set up next check (15 minutes for active calls)
      tokenRefreshTimeoutRef.current = setTimeout(checkAndRefreshToken, 15 * 60 * 1000);
    };

    // Initial check immediately for active calls
    checkAndRefreshToken();

    // Cleanup function
    return () => {
      if (tokenRefreshTimeoutRef.current) {
        clearTimeout(tokenRefreshTimeoutRef.current);
        tokenRefreshTimeoutRef.current = null;
      }
    };
  }, [callStatus, activeCall, refreshTokens, refreshRTCToken, tokens]);

  // Separate cleanup effect that only runs on unmount or when call status changes significantly
  useEffect(() => {
    // Return cleanup function
    return () => {
      // Only stop tracks when the component is unmounting or call status changes significantly
      if (callStatus !== "connected") {
        try {
          // Stop all remote video tracks
          remoteUsers.forEach(user => {
            if (user.videoTrack) {
              try {
                user.videoTrack.stop();
              } catch (err) {
                console.warn(`Non-critical: Error stopping video for user ${user.uid}:`, err);
              }
            }
          });

          // Clear our tracking maps
          playingTracksRef.current.clear();

          // Clear any token refresh timeout
          if (tokenRefreshTimeoutRef.current) {
            clearTimeout(tokenRefreshTimeoutRef.current);
            tokenRefreshTimeoutRef.current = null;
          }
        } catch (cleanupErr) {
          console.error("Error during video cleanup:", cleanupErr);
        }
      }
    };
  }, [callStatus]);

  // Close dialog and end call
  const handleDismiss = () => {
    console.log("User ended call from dialog");
    onEndCall();
    setIsDialogOpen(false);
  };

  // Get user initials for avatar
  const getUserInitials = (name) => {
    if (!name) return "?";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Helper function to render network quality bars
  const renderNetworkQuality = (quality) => {
    if (!quality) return null;

    // Use uplink quality for outgoing, downlink for incoming
    const qualityLevel = activeCall?.isOutgoing
      ? quality.uplinkNetworkQuality
      : quality.downlinkNetworkQuality;

    // Default to 0 (unknown) if undefined
    const level = typeof qualityLevel === 'undefined' ? 0 : qualityLevel;

    // Render 5 bars with appropriate active state based on quality level
    // Lower number = better quality (1 is best, 6 is worst)
    return (
      <div className={styles.networkQualityIndicator}>
        {[5, 4, 3, 2, 1].map((barLevel) => (
          <div
            key={`network-bar-${barLevel}`}
            className={`${styles.networkBar} ${level <= barLevel ? styles.networkBarActive : ''}`}
            style={{ height: `${6 + barLevel * 2}px` }}
          />
        ))}
      </div>
    );
  };

  // Only render if there's an active call
  if (!activeCall) return null;

  return (
    <Dialog
      hidden={!isDialogOpen}
      onDismiss={handleDismiss}
      dialogContentProps={{
        type: DialogType.normal,
        showCloseButton: false,
        title: null,
        subText: null,
      }}
      modalProps={{
        isBlocking: true,
        styles: {
          main: {
            ...styles.callDialog,
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            maxWidth: '85%', // Increased from 70%
            width: '85%', // Increased from 70%
            height: '80vh', // Increased from 70vh
            margin: 0,
            padding: 0,
            boxShadow: '0 0 20px rgba(0, 0, 0, 0.3)',
            '@media (max-width: 992px)': {
              maxWidth: '90%',
              width: '90%',
              height: '75vh',
            },
            '@media (max-width: 768px)': {
              maxWidth: '95%',
              width: '95%',
              height: '70vh',
            },
            '@media (max-width: 576px)': {
              maxWidth: '98%',
              width: '98%',
              height: '65vh',
            },
          },
          // Ensure the overlay covers the entire screen
          layer: {
            zIndex: 1000,
          },
          // Make sure the dialog content is visible
          scrollableContent: {
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            padding: 0,
          }
        },
        dragOptions: undefined, // Disable dragging to prevent positioning issues
      }}
    >
      {/* Call Header with user info */}
      <div className={styles.callHeader}>
        <div className={styles.headerLeft}>
          <div className={styles.personaContainer}>
            <Persona
              imageInitials={getUserInitials(activeCall.peerName)}
              text={activeCall.peerName}
              size={PersonaSize.size32}
              hidePersonaDetails
            />
            <Text className={styles.callStatusText} style={{ marginLeft: '10px' }}>
              {CallStatusText[callStatus]}
            </Text>
          </div>
        </div>

        <div className={styles.headerRight}>
          {callStatus === "connected" && (
            <Text className={styles.callDurationText}>
              {formatCallDuration(callDuration)}
            </Text>
          )}

          {networkQuality && renderNetworkQuality(networkQuality)}

          {/* Token refresh warning */}
          {tokenRefreshNeeded && (
            <Text style={{
              color: '#ea4335',
              fontSize: '12px',
              marginLeft: '10px',
              backgroundColor: 'rgba(234, 67, 53, 0.1)',
              padding: '4px 8px',
              borderRadius: '4px'
            }}>
              Connection issue detected
            </Text>
          )}
        </div>
      </div>

      {/* Call Body */}
      <div className={styles.callBody}>
        {callStatus === "connecting" ? (
          <div className={styles.connectingContainer}>
            <Spinner size={SpinnerSize.large} label="Connecting..." className={styles.connectingSpinner} />
            <Text className={styles.connectingText}>
              Establishing video connection...
            </Text>
            {longConnecting && (
              <Text className={styles.connectingSubText}>
                Taking longer than expected. The recipient may be unavailable or there might be connection issues.
              </Text>
            )}
          </div>
        ) : (
          <div className={styles.videoContainer}>
            {/* Remote Video */}
            <div className={styles.remoteVideoContainer}>
              {remoteUsers.length > 0 ? (
                remoteUsers.map(user => (
                  <div
                    key={user.uid}
                    id={`remote-video-${user.uid}`}
                    ref={el => remoteVideoRefs.current[user.uid] = el}
                    style={{
                      width: "100%",
                      height: "100%",
                      position: "absolute",
                      top: 0,
                      left: 0,
                      backgroundColor: "#202124",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      overflow: "hidden",
                      zIndex: 1,
                      minHeight: "500px", // Increased from 400px
                      objectFit: "cover", // Ensure video fills container
                      objectPosition: "center", // Center the video
                    }}
                  >
                    {/* Remote user info pill */}
                    <div className={styles.remoteUserInfo}>
                      <Persona
                        imageInitials={getUserInitials(activeCall.peerName)}
                        size={PersonaSize.size24}
                        hidePersonaDetails
                      />
                      <span className={styles.remoteUserName}>{activeCall.peerName}</span>
                      <span className={styles.remoteAudioStatus}>
                        <i className={`ms-Icon ms-Icon--${user.hasAudio ? 'Microphone' : 'MicOff'}`}
                           style={{ color: user.hasAudio ? '#8ab4f8' : '#ea4335' }} />
                      </span>
                    </div>

                    {/* Fallback content if video isn't showing */}
                    {!(remoteVideoPlaying[user.uid] || (user.videoTrack && user.hasVideo)) && (
                      <div style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        textAlign: "center",
                        color: "white",
                        zIndex: 1,
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center"
                      }}>
                        <div className={styles.avatarPlaceholder}>
                          {getUserInitials(activeCall.peerName)}
                        </div>
                        <div style={{ fontSize: "18px" }}>{activeCall.peerName}</div>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className={styles.videoPlaceholder}>
                  <div className={styles.avatarPlaceholder}>
                    {getUserInitials(activeCall.peerName)}
                  </div>
                  <div>Waiting for {activeCall.peerName} to join...</div>
                </div>
              )}
            </div>

            {/* Local Video */}
            <div className={styles.localVideoContainer}>
              {isVideoEnabled ? (
                <div
                  ref={localVideoRef}
                  style={{
                    width: "100%",
                    height: "100%",
                    position: "relative",
                    overflow: "hidden",
                    borderRadius: "12px",
                    objectFit: "cover", // Ensure video fills container
                    objectPosition: "center", // Center the video
                  }}
                />
              ) : (
                <div className={styles.videoPlaceholder} style={{ borderRadius: "12px" }}>
                  <div style={{ fontSize: "24px", marginBottom: "4px" }}>
                    {getUserInitials("You")}
                  </div>
                  <div style={{ fontSize: "12px" }}>Camera Off</div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Call Controls */}
        <div className={styles.callControls}>
          {/* Toggle Video Button */}
          <TooltipHost content={isVideoEnabled ? "Turn off camera" : "Turn on camera"}>
            <IconButton
              className={`${styles.controlButton} ${isVideoEnabled ? styles.toggleButton : styles.disabledButton}`}
              iconProps={{ iconName: isVideoEnabled ? "Video" : "VideoOff", styles: styles.iconStyles }}
              onClick={onToggleVideo}
            />
          </TooltipHost>

          {/* Toggle Audio Button */}
          <TooltipHost content={isAudioEnabled ? "Mute microphone" : "Unmute microphone"}>
            <IconButton
              className={`${styles.controlButton} ${isAudioEnabled ? styles.toggleButton : styles.disabledButton}`}
              iconProps={{ iconName: isAudioEnabled ? "Microphone" : "MicOff", styles: styles.iconStyles }}
              onClick={onToggleAudio}
            />
          </TooltipHost>

          {/* End Call Button */}
          <TooltipHost content="End call">
            <IconButton
              className={`${styles.controlButton} ${styles.endCallButton}`}
              iconProps={{ iconName: "DeclineCall", styles: styles.iconStyles }}
              onClick={onEndCall}
            />
          </TooltipHost>
        </div>
      </div>
    </Dialog>
  );
};

export default VideoCallComponent;
