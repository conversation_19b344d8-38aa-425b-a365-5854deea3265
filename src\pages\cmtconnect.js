"use client"

import { useMemo, useCallback, useState, useContext, useEffect, useRef } from "react"
import Navbar from "../components/Navbar"
import Sidebar from "../components/sidebar"
import { SearchBox } from "@fluentui/react/lib/SearchBox"
import { DefaultButton, CommandButton, IconButton } from "@fluentui/react/lib/Button"
import { Icon } from "@fluentui/react"
import { Persona, PersonaPresence, PersonaSize } from "@fluentui/react"
import { Dropdown } from "@fluentui/react/lib/Dropdown"
import { MessageBar, MessageBarType } from "@fluentui/react"
import { Callout, DirectionalHint } from "@fluentui/react/lib/Callout"
import { AuthContext } from "../context/AuthContext"
import ChatThreadView from "../components/ChatThreadView"
import MessageInput from "../components/MessageInput"
import useChat from "../hooks/useChat"
import "../index.css"
import { chunk } from "lodash"
import CallComponent from "../components/CallComponent"
import IncomingCallModal from "../components/IncomingCallModal"
import useCall from "../hooks/useCall"
import { useVideoCallContext } from "../context/VideoCallContext"

const CMTConnect = () => {
  const [sidebarToggled, setSidebarToggled] = useState(false)
  const [searchText, setSearchText] = useState("")
  const [activeFilter, setActiveFilter] = useState("all") // all, speakers, online, recent
  const [selectedIndustry, setSelectedIndustry] = useState(null)
  const [filteredAttendees, setFilteredAttendees] = useState([])
  const [selectedAttendee, setSelectedAttendee] = useState(null)
  const [isSubscribingPresence, setIsSubscribingPresence] = useState(false)
  const [showIndustryFilter, setShowIndustryFilter] = useState(false)
  const industryFilterButtonRef = useRef(null)

  const [error, setError] = useState(null)

  // Get video call functionality
  const { startCall: startVideoCall, setChatService: setVideoCallChatService } = useVideoCallContext()

  // Message thread scroll reference
  const messageEndRef = useRef(null)

  // Track if presence has been initialized
  const presenceInitialized = useRef(false)

  // Get attendees and user info from context
  const { attendees, user, event, tokens } = useContext(AuthContext)

  const {
    connectionStatus,
    error: chatError,
    onlineUsers,
    loadConversation,
    sendMessage,
    resendMessage,
    sendTypingIndicator,
    getMessages,
    getUnreadCount,
    getMissedCallCount,
    isOnline,
    isTyping,
    clearError,
    publishPresence,
    publishOnlineStatus,
    subscribeToPresence,
    sendConversationReadReceipt,
    sendMessageReadReceipt,
    markConversationAsRead,
    sendCallActivityMessage
  } = useChat(user?.agoraid?.toString(), tokens?.chatToken)
    let rtcToken = null;

  // if (user?.agoraid === "i43w25b73m") {
  //   // Fallback token for testing
  //   rtcToken = "007eJxSYOh7WKe9/Y9K3t1r7hUGvUHTFt5tL1zmw3q1b3VFvWq/pLUCg4mluUFScpqFsVFKmkliclqiZaKhpWFqkmlyUlpyinmKz9GkjAAHRoY1kQ4MjAyMDCwMjAwgPhOYZAaTLGCSgYGLIdPEuNzINMncOBcQAAD//6KfIwg=";
  // }
  // else if (user?.agoraid === "rc03of2eme") {
  //   // Fallback token for testing
  //   rtcToken = "007eJxSYBAN3Xa78PUxnafpG5v25NYU/TUouXX3fmT4ufsX7G+3+yYoMJhYmhskJadZGBulpJkkJqclWiYaWhqmJpkmJ6Ulp5inxB1NyghwYGQwWFHEysjAyMDCwMgA4jOBSWYwyQImGRi4GIqSDYzz04xSc1MBAQAA//+rsCZW";
  // }
  const {
    callStatus,
    error: callError,
    activeCall,
    incomingCall,
    isMuted,
    callDuration,
    callHistory,
    formatCallDuration,
    startCall,
    answerCall,
    rejectCall,
    endCall,
    toggleMute,
    clearError: clearCallError,
    localTrackState,
    remoteUsers,
    setAudioEnabled,
    connectionState,
    networkQuality,
    resetCall,
    setChatService
  } = useCall(
    user?.agoraid?.toString(),
    tokens?.rtmToken,
    tokens?.rtcToken,
    user, event
  )

  const toggleSidebar = () => {
    setSidebarToggled((prev) => !prev)
  }

  const toggleIndustryFilter = () => {
    setShowIndustryFilter((prev) => !prev)
  }

  // Process attendees data
  useEffect(() => {
    if (attendees) {
      filterAttendees()
    }
  }, [attendees, searchText, activeFilter, selectedIndustry, onlineUsers])

  useEffect(() => {
    const subscribeToAttendeesPresence = async () => {
      if (
        connectionStatus === "connected" &&
        attendees &&
        attendees.length > 0 &&
        !isSubscribingPresence &&
        !presenceInitialized.current
      ) {
        try {
          setIsSubscribingPresence(true)

          // Publish our online status first to ensure bidirectional sync
          await publishOnlineStatus()

          // Extract attendee IDs (excluding current user)
          const attendeeIds = attendees
            .filter((a) => a.agoraid && a.agoraid.toString() !== user?.agoraid?.toString())
            .map((a) => a.agoraid.toString())

          if (attendeeIds.length > 0) {
            console.log(`Subscribing to presence for ${attendeeIds.length} attendees with bidirectional sync`)

            // Process attendees in batches to avoid overloading
            const batchSize = 20
            const batches = chunk(attendeeIds, batchSize)

            for (const batch of batches) {
              await subscribeToPresence(batch)
              // Small delay between batches for better performance
              await new Promise(resolve => setTimeout(resolve, 200))
            }

            console.log("Bidirectional presence subscription completed successfully")
          }

          presenceInitialized.current = true
        } catch (error) {
          console.error("Error subscribing to attendees presence:", error)
        } finally {
          setIsSubscribingPresence(false)
        }
      }
    }

    subscribeToAttendeesPresence()
  }, [connectionStatus, attendees, subscribeToPresence, publishOnlineStatus, user?.agoraid, isSubscribingPresence])

  // Load conversation when selected attendee changes
  useEffect(() => {
    if (selectedAttendee && connectionStatus === "connected") {
      markConversationAsRead(selectedAttendee.agoraid.toString())
      sendConversationReadReceipt(user.agoraid.toString())
      sendMessageReadReceipt(selectedAttendee.agoraid.toString())
    }
  }, [selectedAttendee, connectionStatus])

  // Set up visibility change detection for presence updates
  useEffect(() => {
    // Handler for visibility change
    const handleVisibilityChange = () => {
      if (connectionStatus === "connected") {
        publishPresence({
          description: document.visibilityState === "visible" ? "online" : "away",
          ext: document.visibilityState === "visible" ? "online" : "away"
        })
      }
    }

    // Add event listener for visibility change
    document.addEventListener("visibilitychange", handleVisibilityChange)

    // Clean up
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [connectionStatus, publishPresence])

  // Set up periodic presence refresh for bidirectional sync
  useEffect(() => {
    let presenceRefreshInterval

    if (connectionStatus === "connected" && presenceInitialized.current) {
      // Refresh presence status every 5 minutes to ensure sync
      presenceRefreshInterval = setInterval(async () => {
        try {
          console.log("Performing periodic presence refresh for bidirectional sync")
          await publishOnlineStatus()

          // Re-fetch presence for all subscribed users
          const attendeeIds = attendees
            ?.filter((a) => a.agoraid && a.agoraid.toString() !== user?.agoraid?.toString())
            ?.map((a) => a.agoraid.toString()) || []

          if (attendeeIds.length > 0) {
            const batchSize = 20
            const batches = chunk(attendeeIds, batchSize)

            for (const batch of batches) {
              await subscribeToPresence(batch)
              await new Promise(resolve => setTimeout(resolve, 100))
            }
          }
        } catch (error) {
          console.error("Error during periodic presence refresh:", error)
        }
      }, 5 * 60 * 1000) // 5 minutes
    }

    return () => {
      if (presenceRefreshInterval) {
        clearInterval(presenceRefreshInterval)
      }
    }
  }, [connectionStatus, attendees, user?.agoraid, publishOnlineStatus, subscribeToPresence])

  // Scroll to bottom of message thread when new messages arrive
  // useEffect(() => {
  //   if (selectedAttendee && messageEndRef.current) {
  //     messageEndRef.current.scrollIntoView({ behavior: "smooth" });
  //   }
  // }, [
  //   selectedAttendee,
  //   getMessages(selectedAttendee?.agoraid?.toString() || ""),
  // ]);

  // Close industry filter dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        showIndustryFilter &&
        industryFilterButtonRef.current &&
        !industryFilterButtonRef.current.contains(event.target) &&
        !event.target.closest(".industry-filter-dropdown")
      ) {
        setShowIndustryFilter(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [showIndustryFilter])

  // Filter attendees based on current filters
  const filterAttendees = useCallback(() => {
    if (!attendees) return

    let filtered = [...attendees]

    // Apply text search
    if (searchText) {
      const searchLower = searchText.toLowerCase()
      filtered = filtered.filter(
        (a) =>
          a.fullname.toLowerCase().includes(searchLower) ||
          (a.companyname && a.companyname.toLowerCase().includes(searchLower)) ||
          (a.jobtitle && a.jobtitle.toLowerCase().includes(searchLower)),
      )
    }

    // Apply filter type
    if (activeFilter === "speakers") {
      filtered = filtered.filter((a) => a.attendeetype === 6)
    } else if (activeFilter === "online") {
      filtered = filtered.filter((a) => isOnline(a.agoraid.toString()))
    } else if (activeFilter === "recent") {
      // In a real implementation, sort by last message time
      // For this example, we'll just take the first 10
      filtered = filtered.slice(0, 10)
    }

    // Apply industry filter
    if (selectedIndustry) {
      filtered = filtered.filter((a) => a.industrysegment === selectedIndustry)
    }

    // Sort online users first, then by unread items (messages + missed calls), then by name
    filtered.sort((a, b) => {
      const aOnline = isOnline(a.agoraid.toString())
      const bOnline = isOnline(b.agoraid.toString())

      if (aOnline && !bOnline) return -1
      if (!aOnline && bOnline) return 1

      // Then sort by combined unread messages and missed calls
      const aUnread = getUnreadCount(a.agoraid.toString()) + getMissedCallCount(a.agoraid.toString())
      const bUnread = getUnreadCount(b.agoraid.toString()) + getMissedCallCount(b.agoraid.toString())

      if (aUnread > bUnread) return -1
      if (aUnread < bUnread) return 1

      // Finally sort by name
      return a.fullname.localeCompare(b.fullname)
    })

    setFilteredAttendees(filtered)

    // Set default selected attendee if none is selected
    if (!selectedAttendee && filtered.length > 0) {
      setSelectedAttendee(filtered[0])
    }
  }, [attendees, searchText, activeFilter, selectedIndustry, isOnline, getUnreadCount, getMissedCallCount, selectedAttendee])

  // Get unique industry segments for the dropdown
  const getIndustrySegments = useMemo(() => {
    if (!attendees) return []

    const segments = [...new Set(attendees.map((a) => a.industrysegment))].filter(Boolean)

    return segments.map((segment) => ({
      key: segment,
      text: segment,
    }))
  }, [attendees])

  // Get persona presence based on online status and custom status
  const getPersonaPresence = useCallback(
    (attendee) => {
      if (!attendee) return PersonaPresence.offline

      const attendeeId = attendee.agoraid.toString()

      if (isOnline(attendeeId)) {
        // First check typing status
        if (isTyping(attendeeId)) return PersonaPresence.busy

        // Then check custom status
        switch (attendee.customStatus) {
          case "Busy":
          case "Do not disturb":
          case "In a meeting":
            return PersonaPresence.dnd
          case "Away":
          case "Out of office":
            return PersonaPresence.away
          default:
            return PersonaPresence.online
        }
      }

      return PersonaPresence.offline
    },
    [isOnline, isTyping],
  )

  // Handle sending new message
  const handleSendMessage = async (text) => {
    if (!selectedAttendee) return false

    try {
      await sendMessage(selectedAttendee.agoraid.toString(), text)
      return true
    } catch (err) {
      console.error("Error sending message:", err)
      return false
    }
  }

  // Handle typing events
  const handleTyping = (isTypingNow) => {
    if (selectedAttendee) {
      sendTypingIndicator(selectedAttendee.agoraid.toString(), isTypingNow)
    }
  }

  // Handle resending a failed message
  const handleResendMessage = async (messageId) => {
    if (!selectedAttendee) return

    try {
      await resendMessage(selectedAttendee.agoraid.toString(), messageId)
    } catch (err) {
      console.error("Error resending message:", err)
    }
  }

  // Select an attendee and mark conversation as read
  const selectAttendee = (attendee) => {
    setSelectedAttendee(attendee)
    if (connectionStatus === "connected") {
      sendMessageReadReceipt(attendee.agoraid.toString())
    }
  }

  // Get initials from name for avatar
  const getInitials = (name) => {
    if (!name) return "?"
    const parts = name.split(" ")
    if (parts.length === 1) return parts[0].charAt(0).toUpperCase()
    return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase()
  }

  // Try to reconnect if connection is lost
  useEffect(() => {
    let reconnectTimer

    if (connectionStatus === "disconnected" && user?.agoraid && tokens?.rtmToken) {
      // Wait 5 seconds before attempting to reconnect
      reconnectTimer = setTimeout(() => {
        // Re-initialize presence subscription
        presenceInitialized.current = false
        // useChat's connectToChat is called internally
      }, 5000)
    }

    return () => {
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
      }
    }
  }, [connectionStatus, user?.agoraid, tokens?.rtmToken])

  const selectedMessages = useMemo(
    () => getMessages(selectedAttendee?.agoraid?.toString() || ""),
    [selectedAttendee, getMessages],
  )

  // Merge errors from chat and call
  useEffect(() => {
    if (callError) {
      setError(callError)
    }
  }, [callError])

  const handleStartCall = async (recipientId, recipientName, recipientUserId) => {
    try {
      if (activeCall) {
        setError("You're already in a call. Please end the current call first.")
        return
      }

      if (!recipientId || !recipientName) {
        setError("Invalid recipient information")
        return
      }

      // if (!tokens?.rtcToken) {
      //   setError("Missing RTC token. Please refresh the page and try again.")
      //   return
      // }

      console.log("Starting call to:", recipientName, "with ID:", recipientId)
      const success = await startCall(recipientId, recipientName, recipientUserId)

      if (!success) {
        setError("Failed to start call. Please check your microphone permissions and try again.")
      }
    } catch (err) {
      console.error("Error starting call:", err)
      setError(`Failed to start call: ${err.message}`)
    }
  }

  const clearAllErrors = () => {
    clearError() // Clear chat errors
    clearCallError() // Clear call errors
  }

  // Add a useEffect to handle microphone permissions
  useEffect(() => {
    // Check for microphone permissions when the component mounts
    const checkMicrophonePermissions = async () => {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true })
        // Permission granted
      } catch (err) {
        console.error("Microphone permission error:", err)
        setError("Microphone access is required for voice calls. Please grant permission in your browser settings.")
      }
    }

    checkMicrophonePermissions()
  }, [])

  // Function to handle call button click with safety check
  const handleCallButtonClick = useCallback(() => {
    if (activeCall) {
      // If we're already in a call, end it
      endCall();
    } else if (callStatus === "connecting") {
      // If we're stuck in connecting state, reset the call
      console.log("Call appears to be stuck in connecting - resetting");
      resetCall();
    } else {
      // Otherwise start a new call (voice call)
      handleStartCall(selectedAttendee.agoraid, selectedAttendee.fullname, selectedAttendee.id);
    }
  }, [activeCall, callStatus, endCall, resetCall, handleStartCall, selectedAttendee]);

  // Function to handle video call button click
  const handleVideoCallButtonClick = useCallback(() => {
    if (!selectedAttendee) return;

    try {
      // Check if we have a valid RTC token
      if (!tokens?.rtcToken) {
        setError("Missing RTC token. Please refresh the page and try again.");
        return;
      }

      console.log(`Starting video call to ${selectedAttendee.fullname} (${selectedAttendee.agoraid})`);
      startVideoCall(selectedAttendee.agoraid, selectedAttendee.fullname);
    } catch (err) {
      console.error("Error starting video call:", err);
      setError(`Failed to start video call: ${err.message}`);
    }
  }, [selectedAttendee, startVideoCall, tokens?.rtcToken]);

  // Connect useCall with useChat for call activity tracking
  useEffect(() => {
    if (connectionStatus === "connected" && typeof sendCallActivityMessage === "function") {
      const chatService = {
        sendCallActivityMessage
      };

      // Connect voice call service to chat service
      setChatService(chatService);
      console.log("Connected voice call service to chat for call activity tracking");

      // Connect video call service to chat service
      setVideoCallChatService(chatService);
      console.log("Connected video call service to chat for call activity tracking");
    }
  }, [connectionStatus, sendCallActivityMessage, setChatService, setVideoCallChatService]);

  return (
    <div className={`home-container ${sidebarToggled ? "sidebar-open" : ""}`}>
      <Navbar toggleSidebar={toggleSidebar} connectionStatus={connectionStatus} publishPresence={publishPresence} />
      <div
        className="d-flex h-100"
        style={{
          background: "#efefef",
          boxSizing: "border-box",
          overflow: "hidden",
        }}
      >
        <Sidebar sidebarToggled={sidebarToggled} toggleSidebar={toggleSidebar} />
        <div className="content w-100 overflow-auto" style={{ boxSizing: "border-box" }}>
          {chatError && (
            <MessageBar
              messageBarType={MessageBarType.error}
              isMultiline={false}
              onDismiss={clearError}
              dismissButtonAriaLabel="Close"
            >
              {chatError}
            </MessageBar>
          )}

          <div className="row m-0 h-100">
            {/* Contacts Sidebar */}
            <div className="col-12 col-lg-3 px-0 bg-white border-right">
              {/* Search bar and industry filter icon */}
              <div className="px-3 py-3 border-bottom bg-light-grey">
                <div className="d-flex align-items-center">
                  <div className="flex-grow-1 position-relative">
                    <SearchBox
                      placeholder="Search"
                      onChange={(_, newValue) => setSearchText(newValue || "")}
                      value={searchText}
                      styles={{ root: { width: "100%" } }}
                    />
                  </div>
                  <div className="ms-2">
                    <IconButton
                      iconProps={{ iconName: "Filter" }}
                      title="Filter by Industry"
                      ariaLabel="Filter by Industry"
                      onClick={toggleIndustryFilter}
                      className={selectedIndustry ? "ms-2 text-primary" : "ms-2"}
                      elementRef={industryFilterButtonRef}
                      styles={{
                        root: {
                          color: selectedIndustry ? "#0078d4" : "#333",
                          borderRadius: "50%",
                          width: "32px",
                          height: "32px",
                          backgroundColor: selectedIndustry ? "#e6f2fa" : "#f0f0f0",
                          transition: "all 0.2s ease",
                          "&:hover": {
                            backgroundColor: selectedIndustry ? "#d0e7f8" : "#e0e0e0",
                          },
                        },
                        icon: {
                          fontSize: "16px",
                        },
                      }}
                    />
                    {showIndustryFilter && (
                      <Callout
                        gapSpace={0}
                        target={industryFilterButtonRef.current}
                        onDismiss={() => setShowIndustryFilter(false)}
                        directionalHint={DirectionalHint.bottomRightEdge}
                        className="industry-filter-dropdown"
                        styles={{
                          calloutMain: {
                            width: "250px",
                            padding: "12px",
                            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
                            borderRadius: "4px",
                          },
                        }}
                      >
                        <div>
                          <h5 className="mb-3">Filter by Industry</h5>
                          <Dropdown
                            placeholder="Select Industry Segment"
                            options={getIndustrySegments}
                            selectedKey={selectedIndustry}
                            onChange={(_, option) => {
                              setSelectedIndustry(option?.key || null)
                              setShowIndustryFilter(false)
                            }}
                            styles={{ dropdown: { width: "100%" } }}
                          />
                          {selectedIndustry && (
                            <DefaultButton
                              text="Clear Filter"
                              onClick={() => {
                                setSelectedIndustry(null)
                                setShowIndustryFilter(false)
                              }}
                              styles={{
                                root: {
                                  marginTop: "10px",
                                  fontSize: "12px",
                                  padding: "4px 8px",
                                  width: "100%",
                                },
                              }}
                            />
                          )}
                        </div>
                      </Callout>
                    )}
                  </div>
                </div>
              </div>

              {/* Filter options */}
              <div className="px-3 py-2 border-bottom bg-light-grey d-flex justify-content-between">
                <div className="d-flex">
                  <CommandButton
                    className={activeFilter === "all" ? "fw-bold" : ""}
                    text="All"
                    onClick={() => setActiveFilter("all")}
                  />
                  <CommandButton
                    className={activeFilter === "speakers" ? "fw-bold" : ""}
                    text="Speakers"
                    onClick={() => setActiveFilter("speakers")}
                  />
                  <CommandButton
                    className={activeFilter === "online" ? "fw-bold" : ""}
                    text="Online"
                    onClick={() => setActiveFilter("online")}
                  />
                  <CommandButton
                    className={activeFilter === "recent" ? "fw-bold" : ""}
                    text="Recent"
                    onClick={() => setActiveFilter("recent")}
                  />
                </div>
              </div>

              {/* Attendees list */}
              <div className="attendees-list overflow-auto" style={{ height: "calc(100vh - 212px)" }}>
                {connectionStatus === "connecting" && (
                  <div className="p-3 text-center">
                    <div className="spinner-border text-primary" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                    <p className="mt-2">Connecting to chat service...</p>
                  </div>
                )}

                {(connectionStatus === "connected" || connectionStatus === "disconnected") && (
                  <>
                    {filteredAttendees && filteredAttendees.length > 0 ? (
                      filteredAttendees.map((attendee) => {
                        const unreadCount = getUnreadCount(attendee.agoraid)
                        const missedCallCount = getMissedCallCount(attendee.agoraid)
                        const totalUnread = unreadCount + missedCallCount

                        return (
                          <div
                            key={attendee.agoraid}
                            className={`px-3 border-bottom py-2 contact-list ${
                              selectedAttendee?.agoraid === attendee.agoraid ? "bg-light" : ""
                            }`}
                            onClick={() => selectAttendee(attendee)}
                            style={{ cursor: "pointer" }}
                          >
                            <div className="d-flex align-items-start justify-content-between">
                              <div className="d-flex align-items-start">
                                <div className="position-relative">
                                  <Persona
                                    text={attendee.fullname}
                                    imageInitials={getInitials(attendee.fullname)}
                                    presence={getPersonaPresence(attendee)}
                                    size={PersonaSize.size48}
                                    hidePersonaDetails
                                  />
                                </div>
                                <div className="ms-2">
                                  <p className="m-0">
                                    <strong>{attendee.fullname}</strong>
                                  </p>
                                  <p
                                    className="m-0"
                                    style={{
                                      fontSize: "0.875rem",
                                      color: "#555",
                                    }}
                                  >
                                    {attendee.jobtitle || ""}
                                  </p>
                                  <p
                                    className="m-0"
                                    style={{
                                      fontSize: "0.75rem",
                                      color: "#888",
                                    }}
                                  >
                                    {attendee.companyname || ""}
                                  </p>
                                  {isTyping(attendee.agoraid.toString()) && (
                                    <p className="m-0 fst-italic text-primary" style={{ fontSize: "0.75rem" }}>
                                      typing...
                                    </p>
                                  )}
                                </div>
                              </div>
                              {totalUnread > 0 && (
                                <div className="align-self-center">
                                  <span className="badge rounded-pill bg-danger d-flex align-items-center">
                                    {unreadCount > 0 && (
                                      <span className="me-1">
                                        <Icon iconName="Message" className="me-1" style={{ fontSize: "0.7rem" }} />
                                        {unreadCount > 99 ? "99+" : unreadCount}
                                      </span>
                                    )}
                                    {missedCallCount > 0 && (
                                      <span className={unreadCount > 0 ? "ms-1 border-start ps-1" : ""}>
                                        <Icon iconName="CallMissed" className="me-1" style={{ fontSize: "0.7rem" }} />
                                        {missedCallCount > 99 ? "99+" : missedCallCount}
                                      </span>
                                    )}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        )
                      })
                    ) : (
                      <div className="p-3 text-center text-muted">No attendees found with current filters</div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Chat window */}
            <div className="col-12 col-lg-9 px-0">
              {selectedAttendee ? (
                <>
                  {/* Contact header */}
                  <div className="bg-light-grey px-3 py-2 border-bottom">
                    <div className="row d-flex justify-content-between align-items-center">
                      <div className="col-6 col-lg-6 d-flex align-items-start">
                        <Persona
                          text={selectedAttendee.fullname}
                          imageInitials={getInitials(selectedAttendee.fullname)}
                          presence={getPersonaPresence(selectedAttendee)}
                          size={PersonaSize.size48}
                          hidePersonaDetails
                        />
                        <div className="ms-2">
                          <p className="m-0">
                            <strong>{selectedAttendee.fullname}</strong>
                          </p>
                          <p className="m-0" style={{ fontSize: "0.875rem", color: "#555" }}>
                            {selectedAttendee.jobtitle || ""}
                          </p>
                          {isTyping(selectedAttendee.agoraid.toString()) && (
                            <p className="m-0 fst-italic text-primary" style={{ fontSize: "0.75rem" }}>
                              typing...
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Add CallComponent */}
                      <CallComponent
                        activeCall={activeCall}
                        callStatus={callStatus}
                        callDuration={callDuration}
                        isMuted={isMuted}
                        localTrackState={localTrackState}
                        remoteUsers={remoteUsers}
                        onEndCall={endCall}
                        onToggleMute={toggleMute}
                        onSetAudioEnabled={setAudioEnabled}
                        formatCallDuration={formatCallDuration}
                        connectionState={connectionState}
                        networkQuality={networkQuality}
                      />

                      {/* Add IncomingCallModal */}
                      <IncomingCallModal incomingCall={incomingCall} onAnswer={answerCall} onReject={rejectCall} />

                      <div className="col-6 col-lg-6 text-end align-items-center">
                        <DefaultButton
                          className={activeCall ? "call-button-active" : "call-button"}
                          onClick={handleCallButtonClick}
                          // disabled={!isOnline(selectedAttendee.agoraid.toString()) && !activeCall && callStatus !== "connecting"}
                          styles={{
                            root: {
                              backgroundColor: activeCall
                                ? "#ff4d4f"
                                : callStatus === "connecting"
                                  ? "#f5a623"
                                  : "#52c41a",
                              color: "white",
                              margin: "0 8px",
                              minWidth: "100px",
                              transition: "all 0.3s ease",
                              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                            },
                            rootHovered: {
                              backgroundColor: activeCall
                                ? "#ff7875"
                                : callStatus === "connecting"
                                  ? "#f7b955"
                                  : "#73d13d",
                              color: "white",
                            },
                            rootDisabled: {
                              backgroundColor: "#d9d9d9",
                              color: "#808080",
                            },
                          }}
                        >
                          <Icon
                            iconName={activeCall
                              ? "DeclineCall"
                              : callStatus === "connecting"
                                ? callStatus === "connecting" && connectionState === "RECONNECTING"
                                  ? "Warning"
                                  : "HourGlass"
                                : "Phone"}
                            className="pe-2"
                          />
                          <span className="d-none d-lg-block pl-2">
                            {activeCall
                              ? "End Call"
                              : callStatus === "connecting"
                                ? connectionState === "RECONNECTING"
                                  ? "Reset Call"
                                  : "Connecting..."
                                : "Call"}
                          </span>
                        </DefaultButton>

                        {/* Video Call Button */}
                        <DefaultButton
                          className="video-call-button"
                          onClick={handleVideoCallButtonClick}
                          // disabled={!isOnline(selectedAttendee.agoraid.toString())}
                          styles={{
                            root: {
                              backgroundColor: "#0078d4",
                              color: "white",
                              margin: "0 8px",
                              minWidth: "100px",
                              transition: "all 0.3s ease",
                              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                            },
                            rootHovered: {
                              backgroundColor: "#106ebe",
                              color: "white",
                            },
                            rootDisabled: {
                              backgroundColor: "#d9d9d9",
                              color: "#808080",
                            },
                          }}
                        >
                          <Icon
                            iconName="Video"
                            className="pe-2"
                          />
                          <span className="d-none d-lg-block pl-2">
                            Video Call
                          </span>
                        </DefaultButton>
                        <DefaultButton
                          className="vcard-button ms-2"
                          disabled={connectionStatus !== "connected"}
                          styles={{
                            root: {
                              backgroundColor: "#f0f0f0",
                              color: "#333",
                            },
                            rootHovered: {
                              backgroundColor: "#e0e0e0",
                            },
                          }}
                        >
                          <Icon iconName="ContactCard" />
                          <span className="d-none d-lg-block pl-2">Send Vcard</span>
                        </DefaultButton>
                      </div>
                    </div>
                  </div>

                  {/* Display any call or chat errors */}
                  {(error || callError) && (
                    <MessageBar
                      messageBarType={MessageBarType.error}
                      onDismiss={clearAllErrors}
                      isMultiline={false}
                      dismissButtonAriaLabel="Close"
                    >
                      {error || callError}
                    </MessageBar>
                  )}

                  {/* Chat area */}
                  <div className="bg-dark-grey d-flex flex-column justify-content-between text-dark">
                    {/* Message thread */}
                    <ChatThreadView
                      connectionStatus={connectionStatus}
                      loadPreviousMessages={loadConversation}
                      selectedAttendee={selectedAttendee}
                      messages={selectedMessages}
                      currentUserId={user?.agoraid?.toString()}
                      onResendMessage={handleResendMessage}
                      isConnected={connectionStatus === "connected"}
                      messageEndRef={messageEndRef}
                      participantStatus={selectedAttendee ? selectedAttendee.customStatus : null}
                    />

                    {/* Connection status indicator for chat */}
                    {connectionStatus !== "connected" && (
                      <div className="p-2 text-center bg-warning text-dark">
                        <small>
                          {connectionStatus === "connecting" ? "Connecting..." : "Disconnected"} - Messages will be sent
                          when reconnected
                        </small>
                      </div>
                    )}

                    {/* Message input */}
                    <MessageInput
                      onSendMessage={handleSendMessage}
                      onTyping={handleTyping}
                      isDisabled={connectionStatus !== "connected"}
                      participantStatus={selectedAttendee ? selectedAttendee.customStatus : null}
                    />
                  </div>
                </>
              ) : (
                <div className="d-flex align-items-center justify-content-center h-100 text-muted">
                  <div className="text-center">
                    <Icon iconName="Chat" style={{ fontSize: "48px" }} />
                    <h4 className="mt-3">Select a contact to start chatting</h4>
                    {connectionStatus !== "connected" && (
                      <p>
                        {connectionStatus === "connecting"
                          ? "Connecting to chat service..."
                          : "Disconnected from chat service. Attempting to reconnect..."}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CMTConnect
