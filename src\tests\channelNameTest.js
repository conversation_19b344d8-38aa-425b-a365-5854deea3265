/**
 * Test file for channel name generation
 * This file tests the new Agora-compliant channel name generation
 */

import { generateChannelName, validateChannelName } from '../utils/agoraConfig';

// Test function to verify channel name generation
const testChannelNameGeneration = () => {
  console.log('=== Testing Channel Name Generation ===');
  
  // Test cases with different user ID lengths
  const testCases = [
    {
      name: 'Short user IDs',
      userId1: 'user1',
      userId2: 'user2',
      callType: 'voice'
    },
    {
      name: 'Medium user IDs',
      userId1: 'i43w25b73m',
      userId2: 'rc03of2eme',
      callType: 'voice'
    },
    {
      name: 'Long user IDs',
      userId1: 'very_long_user_id_12345678901234567890',
      userId2: 'another_very_long_user_id_09876543210987654321',
      callType: 'voice'
    },
    {
      name: 'Video call with medium IDs',
      userId1: 'i43w25b73m',
      userId2: 'rc03of2eme',
      callType: 'video'
    },
    {
      name: 'Consistency test - same users different order',
      userId1: 'rc03of2eme',
      userId2: 'i43w25b73m',
      callType: 'voice'
    }
  ];
  
  const results = [];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n--- Test ${index + 1}: ${testCase.name} ---`);
    
    const channelName = generateChannelName(testCase.userId1, testCase.userId2, testCase.callType);
    const validation = validateChannelName(channelName);
    
    const result = {
      testCase: testCase.name,
      channelName,
      length: channelName.length,
      isValid: validation.isValid,
      error: validation.error || null,
      userId1: testCase.userId1,
      userId2: testCase.userId2,
      callType: testCase.callType
    };
    
    results.push(result);
    
    console.log(`Channel Name: ${channelName}`);
    console.log(`Length: ${channelName.length} bytes`);
    console.log(`Valid: ${validation.isValid}`);
    if (!validation.isValid) {
      console.log(`Error: ${validation.error}`);
    }
    
    // Test character validity
    const validCharRegex = /^[a-zA-Z0-9 !#$%&()+\-:;<=>?@[\]^_{|}~,]*$/;
    const hasValidChars = validCharRegex.test(channelName);
    console.log(`Valid Characters: ${hasValidChars}`);
    
    if (!hasValidChars) {
      console.log('Invalid characters found!');
    }
  });
  
  // Test consistency - same users should generate same channel name (within same timestamp)
  console.log('\n=== Testing Consistency ===');
  const user1 = 'i43w25b73m';
  const user2 = 'rc03of2eme';
  
  // Generate channel names with users in different orders
  const channel1 = generateChannelName(user1, user2, 'voice');
  const channel2 = generateChannelName(user2, user1, 'voice');
  
  console.log(`Channel 1 (${user1}, ${user2}): ${channel1}`);
  console.log(`Channel 2 (${user2}, ${user1}): ${channel2}`);
  
  // They should be identical except for the timestamp part
  const channel1Parts = channel1.split('_');
  const channel2Parts = channel2.split('_');
  
  const samePrefix = channel1Parts[0] === channel2Parts[0];
  const sameHash1 = channel1Parts[1] === channel2Parts[1];
  const sameHash2 = channel1Parts[2] === channel2Parts[2];
  
  console.log(`Same prefix: ${samePrefix}`);
  console.log(`Same hash structure: ${sameHash1 && sameHash2}`);
  console.log(`Consistency test: ${samePrefix && sameHash1 && sameHash2 ? 'PASSED' : 'FAILED'}`);
  
  // Summary
  console.log('\n=== Test Summary ===');
  const allValid = results.every(r => r.isValid);
  const allUnder64 = results.every(r => r.length < 64);
  const maxLength = Math.max(...results.map(r => r.length));
  const minLength = Math.min(...results.map(r => r.length));
  
  console.log(`All channel names valid: ${allValid}`);
  console.log(`All under 64 bytes: ${allUnder64}`);
  console.log(`Length range: ${minLength} - ${maxLength} bytes`);
  
  if (allValid && allUnder64) {
    console.log('✅ All tests PASSED!');
  } else {
    console.log('❌ Some tests FAILED!');
    results.forEach(r => {
      if (!r.isValid || r.length >= 64) {
        console.log(`Failed: ${r.testCase} - ${r.channelName} (${r.length} bytes)`);
      }
    });
  }
  
  return results;
};

// Export for use in other files
export { testChannelNameGeneration };

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  console.log('Channel Name Generation Tests');
  testChannelNameGeneration();
}
